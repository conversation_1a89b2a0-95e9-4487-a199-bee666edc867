# THREE.js 3D自定义图层功能说明

## 功能概述

基于高德地图GLCustomLayer和THREE.js 0.142版本实现的3D自定义图层功能，可以在3D地图模式下渲染各种3D模型和动画效果。

## 主要特性

### 1. 3D模型渲染
- 支持各种THREE.js几何体（立方体、球体、圆锥、圆柱、八面体等）
- 自定义材质和纹理
- 实时光照效果（环境光 + 平行光）

### 2. 动画效果
- 模型旋转动画
- 浮动动画
- 可自定义动画参数

### 3. 地图集成
- 与高德地图3D模式完美集成
- 自动坐标转换
- 相机参数同步
- 支持地图缩放、旋转、倾斜

### 4. 用户控制
- 3D模式下可通过控制面板开关图层显示
- 支持图层的显示/隐藏切换

## 技术实现

### 核心文件
- `src/managers/ThreeJSCustomLayer.js` - 3D图层管理器
- `src/view/YunNanMap.vue` - 地图主组件集成
- `src/components/MapControlPanel.vue` - 控制面板UI

### 关键技术点

#### 1. GLCustomLayer创建
```javascript
const gllayer = new AMap.GLCustomLayer({
  zIndex: 10,
  init: (gl) => {
    // 初始化THREE.js场景、相机、渲染器
  },
  render: () => {
    // 每帧渲染逻辑
  }
});
```

#### 2. 坐标转换
```javascript
const customCoords = map.customCoords;
const coords = customCoords.lngLatsToCoords([
  [101.4, 25.1],  // 经纬度坐标
  [101.6, 25.0]
]);
```

#### 3. 相机同步
```javascript
const { near, far, fov, up, lookAt, position } = customCoords.getCameraParams();
camera.near = near;
camera.far = far;
camera.fov = fov;
camera.position.set(...position);
camera.up.set(...up);
camera.lookAt(...lookAt);
camera.updateProjectionMatrix();
```

## 使用方法

### 1. 切换到3D模式
在地图控制面板中点击"3D地图"按钮切换到3D模式。

### 2. 启用3D图层
在3D模式下，地图显示区域会出现"3D模型图层"开关，点击开启即可显示3D模型。

### 3. 查看效果
- 地图上会显示5个不同形状和颜色的3D模型
- 模型会有旋转和浮动动画效果
- 可以通过地图操作（缩放、旋转、倾斜）查看不同角度的效果

## 模型配置

当前默认创建了5个测试模型：

1. **立方体** - 红色，位于昆明附近
2. **球体** - 青色，位于中心点右侧
3. **圆锥** - 蓝色，位于中心点左下
4. **圆柱** - 黄色，位于右上角
5. **八面体** - 紫色，位于左下角

每个模型都有：
- 不同的颜色和材质
- 独立的旋转动画
- 浮动动画效果
- 不同的高度位置

## 扩展开发

### 添加新的3D模型
在`ThreeJSCustomLayer.js`的`createTestModels`方法中添加新的几何体和材质：

```javascript
// 创建新的几何体
const geometry = new THREE.SphereGeometry(1000, 32, 16);
const material = new THREE.MeshPhongMaterial({
  color: 0xff0000,
  transparent: true,
  opacity: 0.8
});

const mesh = new THREE.Mesh(geometry, material);
mesh.position.set(coord[0], coord[1], height);
scene.add(mesh);
```

### 自定义动画
修改`updateAnimations`方法来实现自定义动画效果：

```javascript
updateAnimations() {
  const time = Date.now() * 0.001;
  
  this.meshes.forEach((item) => {
    // 自定义动画逻辑
    item.mesh.rotation.y = time * 0.5;
    item.mesh.position.z = Math.sin(time) * 500 + 1000;
  });
}
```

### 加载外部模型
可以使用THREE.js的加载器加载外部3D模型文件：

```javascript
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';

const loader = new GLTFLoader();
loader.load('path/to/model.gltf', (gltf) => {
  const model = gltf.scene;
  model.position.set(x, y, z);
  scene.add(model);
});
```

## 注意事项

1. **版本兼容性**：必须使用THREE.js 0.142版本，与高德地图官方示例保持一致
2. **性能考虑**：大量3D模型可能影响性能，建议根据地图缩放级别动态加载
3. **坐标系统**：使用高德地图的customCoords进行坐标转换
4. **GL状态管理**：必须在render函数前后调用`renderer.resetState()`

## 故障排除

### 常见问题

1. **3D模型不显示**
   - 检查是否在3D模式下
   - 确认3D图层开关是否开启
   - 查看浏览器控制台是否有错误信息

2. **动画卡顿**
   - 检查模型数量是否过多
   - 确认材质复杂度
   - 查看浏览器性能

3. **坐标位置不正确**
   - 确认经纬度坐标是否正确
   - 检查坐标转换是否成功
   - 验证渲染中心点设置

### 调试方法

在浏览器控制台中可以查看相关日志：
- 图层初始化状态
- 模型创建信息
- 动画更新状态
- 错误信息

## 更新日志

### v1.0.0 (2024-01-31)
- 初始版本发布
- 支持基本3D模型渲染
- 集成地图控制面板
- 实现动画效果
