/**
 * THREE.js 3D自定义图层管理器
 * 基于高德地图GLCustomLayer和THREE.js实现3D模型渲染
 */

import * as THREE from 'three'

export class ThreeJSCustomLayer {
  constructor(map, AMap, options = {}) {
    this.map = map
    this.AMap = AMap
    this.options = {
      zIndex: 10,
      center: [101.5, 25.0], // 云南省中心坐标
      visible: true,
      ...options
    }

    // THREE.js 相关对象
    this.scene = null
    this.camera = null
    this.renderer = null
    this.customCoords = null
    this.glLayer = null

    // 3D模型数组
    this.meshes = []
    this.animationId = null

    // 初始化状态
    this.isInitialized = false
    this.isVisible = false

    console.log('ThreeJSCustomLayer 管理器已创建')
  }

  /**
   * 初始化3D自定义图层
   */
  async init() {
    try {
      if (this.isInitialized) {
        console.warn('ThreeJSCustomLayer 已经初始化')
        return true
      }

      // 检查地图是否支持3D功能
      // 在高德地图API 2.0中，我们通过检查地图配置来确认3D支持
      console.log('地图实例信息:', {
        pitch: this.map.getPitch ? this.map.getPitch() : 'getPitch方法不存在',
        rotation: this.map.getRotation ? this.map.getRotation() : 'getRotation方法不存在',
        pitchEnable: this.map.getStatus ? this.map.getStatus().pitchEnable : '状态获取失败'
      })

      // 暂时跳过3D模式检查，直接尝试初始化
      console.log('跳过3D模式检查，直接初始化GLCustomLayer')

      // 获取自定义坐标系统
      this.customCoords = this.map.customCoords
      if (!this.customCoords) {
        console.warn('无法获取地图的customCoords对象，尝试其他方式')
        // 在高德地图API 2.0中，customCoords可能需要不同的获取方式
        this.customCoords = this.map.getCustomCoords ? this.map.getCustomCoords() : null
        if (!this.customCoords) {
          throw new Error('无法获取地图的customCoords对象')
        }
      }

      // 创建GLCustomLayer
      this.createGLLayer()

      // 添加图层到地图
      this.map.add(this.glLayer)

      this.isInitialized = true
      this.isVisible = this.options.visible

      console.log('ThreeJSCustomLayer 初始化成功')
      return true

    } catch (error) {
      console.error('ThreeJSCustomLayer 初始化失败:', error)
      return false
    }
  }

  /**
   * 创建GLCustomLayer图层
   */
  createGLLayer() {
    const self = this

    this.glLayer = new this.AMap.GLCustomLayer({
      zIndex: this.options.zIndex,
      visible: this.options.visible,

      // 初始化函数 - 在图层创建时执行一次
      init: (gl) => {
        console.log('GLCustomLayer init 开始')
        
        // 创建透视相机（3D模式）
        self.camera = new THREE.PerspectiveCamera(
          60, // fov
          window.innerWidth / window.innerHeight, // aspect
          100, // near
          1 << 30 // far
        )

        // 创建WebGL渲染器 - 使用外部WebGL上下文时需要特殊处理
        self.renderer = new THREE.WebGLRenderer({
          context: gl, // 使用地图的gl上下文
          alpha: true,
          antialias: false, // 关闭抗锯齿以避免状态冲突
          preserveDrawingBuffer: false,
          premultipliedAlpha: false,
          powerPreference: "high-performance"
        })

        // 重要：必须设置为false，否则地图底图无法显示
        self.renderer.autoClear = false

        // 禁用深度测试和模板测试以避免状态冲突
        self.renderer.sortObjects = false

        // 设置渲染器状态管理
        if (self.renderer.state) {
          // 重置所有WebGL状态到默认值
          if (typeof self.renderer.state.reset === 'function') {
            self.renderer.state.reset()
          }
        }

        // 创建场景
        self.scene = new THREE.Scene()

        // 添加光照
        self.setupLighting()

        // 创建简单的测试3D模型
        self.createSimpleTestModels()

        console.log('GLCustomLayer init 完成')
      },

      // 渲染函数 - 每帧都会执行
      render: () => {
        try {
          if (!self.renderer || !self.scene || !self.camera) return

          // 使用更安全的渲染方式，避免矩阵代理问题
          const gl = self.renderer.getContext()

          // 设置渲染中心点
          self.customCoords.setCenter(self.options.center)

          // 获取相机参数并同步
          const { near, far, fov, up, lookAt, position } = self.customCoords.getCameraParams()

          // 手动设置相机参数，避免使用可能有问题的方法
          self.camera.near = near
          self.camera.far = far
          self.camera.fov = fov

          // 直接设置相机位置和方向，避免复杂的矩阵操作
          self.camera.position.x = position[0]
          self.camera.position.y = position[1]
          self.camera.position.z = position[2]

          self.camera.up.x = up[0]
          self.camera.up.y = up[1]
          self.camera.up.z = up[2]

          // 设置相机朝向
          const lookAtVector = new THREE.Vector3(lookAt[0], lookAt[1], lookAt[2])
          self.camera.lookAt(lookAtVector)

          // 更新投影矩阵
          self.camera.updateProjectionMatrix()

          // 更新模型动画
          self.updateAnimations()

          // 清除深度缓冲，但不清除颜色缓冲
          gl.clear(gl.DEPTH_BUFFER_BIT)

          // 启用必要的WebGL状态
          gl.enable(gl.DEPTH_TEST)
          gl.enable(gl.BLEND)
          gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA)

          // 尝试渲染场景，如果失败则跳过
          try {
            self.renderer.render(self.scene, self.camera)
          } catch (renderError) {
            console.warn('THREE.js渲染失败，跳过此帧:', renderError.message)
          }

        } catch (error) {
          console.error('GLCustomLayer渲染错误:', error)
        }
      }
    })
  }

  /**
   * 设置光照
   */
  setupLighting() {
    try {
      // 环境光
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.6)
      this.scene.add(ambientLight)

      // 平行光
      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
      directionalLight.position.set(1000, -100, 900)
      this.scene.add(directionalLight)

      console.log('光照设置完成')
    } catch (error) {
      console.error('光照设置失败:', error)
    }
  }

  /**
   * 创建测试3D模型
   */
  createTestModels() {
    try {
      // 转换坐标点
      const positions = [
        [101.4, 25.1],  // 昆明附近
        [101.6, 25.0],  // 中心点右侧
        [101.3, 24.9],  // 中心点左下
        [101.7, 25.2],  // 右上角
        [101.2, 24.8]   // 左下角
      ]

      const coords = this.customCoords.lngLatsToCoords(positions)
      console.log('坐标转换结果:', coords)

      // 创建简单的几何体和材质
      const geometries = [
        new THREE.BoxGeometry(2000, 2000, 2000),      // 立方体
        new THREE.SphereGeometry(1200, 8, 6),         // 球体（降低复杂度）
        new THREE.ConeGeometry(1000, 2500, 6),        // 圆锥（降低复杂度）
        new THREE.CylinderGeometry(800, 800, 2000, 6), // 圆柱（降低复杂度）
        new THREE.OctahedronGeometry(1500)            // 八面体
      ]

      const colors = [0xff6b6b, 0x4ecdc4, 0x45b7d1, 0xf9ca24, 0x6c5ce7]

      coords.forEach((coord, index) => {
        try {
          const geometry = geometries[index % geometries.length]
          const material = new THREE.MeshBasicMaterial({
            color: colors[index % colors.length],
            transparent: true,
            opacity: 0.8
          })

          const mesh = new THREE.Mesh(geometry, material)
          mesh.position.set(coord[0], coord[1], 1000 + index * 500) // 不同高度

          // 添加到场景和管理数组
          this.scene.add(mesh)
          this.meshes.push({
            mesh,
            rotationSpeed: {
              x: (Math.random() - 0.5) * 0.01,
              y: (Math.random() - 0.5) * 0.01,
              z: (Math.random() - 0.5) * 0.01
            },
            initialY: coord[1],
            floatOffset: Math.random() * Math.PI * 2
          })

          console.log(`创建3D模型 ${index + 1}:`, {
            type: ['立方体', '球体', '圆锥', '圆柱', '八面体'][index],
            position: [coord[0], coord[1], 1000 + index * 500],
            color: colors[index % colors.length].toString(16)
          })
        } catch (error) {
          console.error(`创建第${index + 1}个3D模型时出错:`, error)
        }
      })

      console.log(`成功创建了 ${this.meshes.length} 个测试3D模型`)
    } catch (error) {
      console.error('创建测试3D模型时出错:', error)
    }
  }

  /**
   * 创建简单的测试3D模型 - 避免复杂材质和光照
   */
  createSimpleTestModels() {
    try {
      console.log('开始创建简单的3D测试模型...')

      // 只创建一个简单的立方体进行测试
      const positions = [[101.5, 25.0]] // 云南省中心
      const coords = this.customCoords.lngLatsToCoords(positions)
      console.log('坐标转换结果:', coords)

      if (coords && coords.length > 0) {
        const coord = coords[0]

        // 创建简单的立方体几何体
        const geometry = new THREE.BoxGeometry(5000, 5000, 5000)

        // 使用最简单的材质 - 不依赖光照
        const material = new THREE.MeshBasicMaterial({
          color: 0xff6b6b,
          transparent: true,
          opacity: 0.8,
          wireframe: false
        })

        const mesh = new THREE.Mesh(geometry, material)
        mesh.position.set(coord[0], coord[1], 2000) // 设置高度

        // 添加到场景
        this.scene.add(mesh)
        this.meshes.push({
          mesh,
          rotationSpeed: { x: 0.01, y: 0.01, z: 0 },
          initialY: coord[1],
          floatOffset: 0
        })

        console.log('创建简单3D模型成功:', {
          position: [coord[0], coord[1], 2000],
          color: '0xff6b6b'
        })
      }

      console.log(`成功创建了 ${this.meshes.length} 个简单测试3D模型`)
    } catch (error) {
      console.error('创建简单测试3D模型时出错:', error)
    }
  }

  /**
   * 更新动画
   */
  updateAnimations() {
    const time = Date.now() * 0.001

    this.meshes.forEach((item, index) => {
      const { mesh, rotationSpeed, initialY, floatOffset } = item

      // 旋转动画
      mesh.rotation.x += rotationSpeed.x
      mesh.rotation.y += rotationSpeed.y
      mesh.rotation.z += rotationSpeed.z

      // 浮动动画
      const floatAmount = Math.sin(time * 2 + floatOffset) * 200
      mesh.position.y = initialY + floatAmount
    })
  }

  /**
   * 显示图层
   */
  show() {
    if (this.glLayer && this.isInitialized) {
      this.glLayer.show()
      this.isVisible = true
      console.log('3D自定义图层已显示')
    }
  }

  /**
   * 隐藏图层
   */
  hide() {
    if (this.glLayer && this.isInitialized) {
      this.glLayer.hide()
      this.isVisible = false
      console.log('3D自定义图层已隐藏')
    }
  }

  /**
   * 切换图层显示状态
   */
  toggle() {
    if (this.isVisible) {
      this.hide()
    } else {
      this.show()
    }
  }

  /**
   * 销毁图层
   */
  destroy() {
    try {
      // 停止动画
      if (this.animationId) {
        cancelAnimationFrame(this.animationId)
        this.animationId = null
      }

      // 清理3D对象
      if (this.scene) {
        this.meshes.forEach(item => {
          this.scene.remove(item.mesh)
          if (item.mesh.geometry) item.mesh.geometry.dispose()
          if (item.mesh.material) item.mesh.material.dispose()
        })
        this.meshes = []
      }

      // 移除图层
      if (this.glLayer && this.map) {
        this.map.remove(this.glLayer)
      }

      // 清理引用
      this.scene = null
      this.camera = null
      this.renderer = null
      this.glLayer = null
      this.customCoords = null

      this.isInitialized = false
      this.isVisible = false

      console.log('ThreeJSCustomLayer 已销毁')
    } catch (error) {
      console.error('销毁ThreeJSCustomLayer时出错:', error)
    }
  }

  /**
   * 获取图层状态
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isVisible: this.isVisible,
      meshCount: this.meshes.length,
      mapMode: this.map ? (this.map.getViewMode ? this.map.getViewMode() : '3D') : 'unknown'
    }
  }
}
